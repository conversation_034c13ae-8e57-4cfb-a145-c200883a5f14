import { Type } from "class-transformer";
import { ShopifyMedia } from "types/prisma-types";

export class ShopifyVariantResponseDto {
  shopifyId: bigint;

  title: string;
  displayName: string;
  sku?: string;
  barcode?: string;
  price: number;
  compareAtPrice?: number;
  position: number;
  taxable: boolean;

  @Type(() => Object)
  media: ShopifyMedia[];

  inventoryQuantity?: number;

  inventoryItemId?: bigint;

  createdAt: Date;
  updatedAt: Date;
  lastSyncedAt?: Date;
  shopifyCreatedAt?: Date;
  shopifyUpdatedAt?: Date;

  shopifyProductId: bigint;

  variantId?: number;

  // Relations
  shopifyProduct?: unknown;
  variant?: unknown;
}
