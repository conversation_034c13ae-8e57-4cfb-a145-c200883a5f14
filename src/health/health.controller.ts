import { Controller, Get, Inject } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import appConfig from "../config/configuration/app.config";
import microserviceConfig from "../config/configuration/microservice.config";

@Controller("health")
export class HealthController {
  constructor(
    @Inject(appConfig.KEY)
    private readonly appConf: ConfigType<typeof appConfig>,
    @Inject(microserviceConfig.KEY)
    private readonly microserviceConf: ConfigType<typeof microserviceConfig>,
  ) {}

  @Get()
  check() {
    return {
      status: "ok",
      timestamp: new Date().toISOString(),
      service: this.microserviceConf.name,
      version: this.appConf.version,
    };
  }
}
