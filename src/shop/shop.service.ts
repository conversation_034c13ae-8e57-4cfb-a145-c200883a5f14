import {
  Inject,
  Injectable,
  Logger,
  On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { ClientProxy } from "@nestjs/microservices";
import { firstValueFrom, timeout } from "rxjs";
import microserviceConfig from "../config/configuration/microservice.config";
import { IShop } from "./shop.interface";

export const SHOP_SERVICE_NAME = "SHOP_SERVICE";

@Injectable()
export class ShopService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ShopService.name);

  constructor(
    @Inject(SHOP_SERVICE_NAME)
    private readonly client: ClientProxy,
    @Inject(microserviceConfig.KEY)
    private readonly microserviceConf: ConfigType<typeof microserviceConfig>,
  ) {}

  async onModuleInit() {
    try {
      await this.client.connect();
      this.logger.log("Successfully connected to shop service");
    } catch (error) {
      this.logger.warn(
        "Failed to connect to shop service during startup. Connection will be attempted when needed.",
        error,
      );
    }
  }

  async onModuleDestroy() {
    try {
      await this.client.close();
    } catch (error) {
      this.logger.warn("Error closing shop service connection", error);
    }
  }

  async getAllShops(): Promise<IShop[]> {
    return this.send<IShop[]>({ cmd: "shop.findAll" }, {});
  }

  async getShop(id: bigint): Promise<IShop | null> {
    return this.send<IShop | null>({ cmd: "shop.findOne" }, { id });
  }

  protected async send<TResult = unknown, TInput = unknown, TPattern = unknown>(
    pattern: TPattern,
    data: TInput,
  ): Promise<TResult> {
    const result$ = this.client
      .send<TResult, TInput>(pattern, data)
      .pipe(timeout(this.microserviceConf.clients.shopService.timeout));

    return await firstValueFrom(result$);
  }
}
