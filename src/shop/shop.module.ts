import { Module } from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { ClientsModule, Transport } from "@nestjs/microservices";
import microserviceConfig from "../config/configuration/microservice.config";
import { SHOP_SERVICE_NAME, ShopService } from "./shop.service";

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: SHOP_SERVICE_NAME,
        inject: [microserviceConfig.KEY],
        useFactory(microserviceConf: ConfigType<typeof microserviceConfig>) {
          return {
            transport: Transport.TCP,
            options: {
              port: microserviceConf.clients.shopService.port,
            },
          };
        },
      },
    ]),
  ],
  providers: [ShopService],
  exports: [ShopService],
})
export class ShopModule {}
