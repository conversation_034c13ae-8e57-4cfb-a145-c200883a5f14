import "json-bigint-patch";
import { Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import { MicroserviceOptions, Transport } from "@nestjs/microservices";
import { useContainer } from "class-validator";
import { AppModule } from "./app.module";
import { BypassGlobalValidationPipe } from "./common/pipes/bypass-global-validation.pipe";
import { IAppConfig } from "./config/interfaces/app.interface";
import { IGraphQLConfig } from "./config/interfaces/graphql.interface";
import { IMicroserviceConfig } from "./config/interfaces/microservice.interface";

async function bootstrap() {
  const logger = new Logger();
  try {
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);
    const appConfig = configService.getOrThrow<IAppConfig>("app");
    const graphqlConfig = configService.getOrThrow<IGraphQLConfig>("graphql");
    const microserviceConfig =
      configService.getOrThrow<IMicroserviceConfig>("microservice");

    app.useGlobalPipes(
      new BypassGlobalValidationPipe({ transform: true, whitelist: true }),
    );

    app.enableCors();

    app.setGlobalPrefix(appConfig.globalPrefix);

    // Enable DI in class-validator
    useContainer(app.select(AppModule), { fallbackOnErrors: true });

    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.TCP,
      options: {
        host: appConfig.hostName,
        port: microserviceConfig.port,
      },
    });

    await app.startAllMicroservices();

    await app.listen(appConfig.port, appConfig.hostName);

    logger.log(`🚀 HTTP API running on port ${appConfig.port}`);
    logger.log(`📍 Global prefix: ${appConfig.globalPrefix}`);
    logger.log(
      `🎯 GraphQL endpoint: ${appConfig.globalPrefix}${graphqlConfig.graphqlPath}`,
    );
    logger.log(`❤️ Health endpoint: ${appConfig.globalPrefix}/health`);

    if (process.env.NODE_ENV === "production") {
      logger.log(
        `🌐 Service Discovery: ${microserviceConfig.name}.${microserviceConfig.discoveryNamespace}`,
      );
    }
  } catch (error) {
    logger.error(`Failed to start application:`, error);
    if (error instanceof AggregateError) {
      logger.error("AggregateError details:");
      error.errors.forEach((err, index) => {
        logger.error(`Error ${index + 1}:`, err);
      });
    }
    process.exit(1);
  }
}

void bootstrap();
