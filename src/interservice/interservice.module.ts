import { Module } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { InterserviceController } from "./interservice.controller";

const pricingHost =
  process.env.PRICING_SERVICE_HOST ??
  `be-pricing-management-microservice.${process.env.SERVICE_DISCOVERY_NAMESPACE ?? "be-pricing-management-microservice.ecom360-production.local"}`;
const pricingPort = parseInt(process.env.PRICING_SERVICE_PORT ?? "3003", 10);

@Module({
  imports: [
    ClientsModule.register([
      {
        name: "PRICING_SERVICE",
        transport: Transport.TCP,
        options: {
          host: pricingHost,
          port: pricingPort,
        },
      },
    ]),
  ],
  controllers: [InterserviceController],
})
export class InterserviceModule {}
