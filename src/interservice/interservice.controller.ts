import { Controller, Get, Inject, Logger } from "@nestjs/common";
import { ClientProxy } from "@nestjs/microservices";
import { firstValueFrom, timeout } from "rxjs";

interface IHealthCheckResponse {
  status: string;
  timestamp: string;
  service: string;
  version: string;
}

@Controller("internal/pricing")
export class InterserviceController {
  private readonly logger = new Logger(InterserviceController.name);

  constructor(
    @Inject("PRICING_SERVICE") private readonly pricingClient: ClientProxy,
  ) {}

  @Get("health")
  async health(): Promise<IHealthCheckResponse> {
    try {
      const result = await firstValueFrom(
        this.pricingClient
          .send<IHealthCheckResponse>("health_check", {})
          .pipe(timeout(5000)),
      );
      return result;
    } catch (err) {
      this.logger.error(`Pricing service health_check failed: ${String(err)}`);
      throw err;
    }
  }
}
