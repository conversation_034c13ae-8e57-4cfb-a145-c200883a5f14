import { Injectable } from "@nestjs/common";
import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";
import { VariantsService } from "../../variants/services/variants.service";

export const IS_UNIQUE_SKU = "IsUniqueSkuConstraint";

@ValidatorConstraint({
  name: IS_UNIQUE_SKU,
  async: true,
})
@Injectable()
export class IsUniqueSkuConstraint implements ValidatorConstraintInterface {
  constructor(private readonly variantsService: VariantsService) {}

  async validate(sku: string): Promise<boolean> {
    return !(await this.variantsService.doesSkuExist(sku));
  }

  defaultMessage(): string {
    return "SKU $value already exists";
  }
}

export function IsUniqueSku(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsUniqueSkuConstraint,
    });
  };
}
