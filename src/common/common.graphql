type Query {
  _test: String
}

type Mutation {
  _test: String
}

type PaginationMeta {
  totalCount: Int!
  currentPage: Int!
  totalPages: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

input PaginationInput {
  page: Int!
  pageSize: Int!
}

input WhereInput {
  operation: WhereOperator!
  value: String!
}

enum SortDirection {
  ASC
  DESC
}

enum WhereOperator {
  IS
  EQUALS
  CONTAINS
  GTE
  LTE
}

interface Node {
  id: Int!
}

interface HasTimestamps {
  createdAt: DateTime!
  updatedAt: DateTime!
}

interface PaginationConnection {
  meta: PaginationMeta!
}

interface ShopifyNode {
  shopifyId: BigInt!
}

interface HasShopifyTimestamps {
  shopifyCreatedAt: DateTime!
  shopifyUpdatedAt: DateTime!
}

interface HasSyncTimestamp {
  lastSyncedAt: DateTime!
}

scalar BigInt
scalar DateTime
scalar JSON
scalar File
