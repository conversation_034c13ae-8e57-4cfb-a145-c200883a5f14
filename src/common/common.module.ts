import { Module } from "@nestjs/common";
import { PaginationService } from "./services/pagination.service";
import { PrismaService } from "./services/prisma.service";
import { QueryMapperService } from "./services/query-mapper.service";

@Module({
  providers: [PaginationService, QueryMapperService, PrismaService],
  exports: [PaginationService, QueryMapperService, PrismaService],
})
export class CommonModule {}
