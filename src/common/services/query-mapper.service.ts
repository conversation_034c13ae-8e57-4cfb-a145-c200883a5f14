import { Injectable } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { snakeCase } from "lodash";
import { OrderByInput } from "../dto/order-by.input";
import { IWhereEntityInput } from "../dto/where.input";
import { WhereOperator } from "../types/where-operator";

export type NumberConvertibleFields = string[];

@Injectable()
export class QueryMapperService {
  mapWhereInput(
    whereInputs: IWhereEntityInput,
    numberConvertibleFields?: NumberConvertibleFields,
  ): Record<string, unknown> {
    const where = Object.entries(whereInputs).reduce<Record<string, unknown>>(
      (acc, [field, whereInput]) => {
        if (!whereInput) {
          return acc;
        }

        const { value, operation } = whereInput;
        const parseValue = this.parseValue(
          value,
          field,
          numberConvertibleFields,
        );

        switch (operation) {
          case WhereOperator.IS:
            acc[field] = parseValue;
            break;

          // TODO see if needed
          case WhereOperator.EQUALS:
            acc[field] = parseValue;
            break;

          case WhereOperator.CONTAINS:
            acc[field] = {
              contains: parseValue,
              mode: "insensitive", // Case-insensitive search
            };
            break;

          case WhereOperator.GTE:
            acc[field] = {
              [WhereOperator.GTE]: parseValue,
            };
            break;
          case WhereOperator.LTE:
            acc[field] = {
              [WhereOperator.LTE]: parseValue,
            };
            break;
          default:
            throw new Error(
              `Unsupported operation: ${whereInput.operation} for field: ${field}`,
            );
        }

        return acc;
      },
      {},
    );

    return where;
  }

  mapWhereInputToSQL(
    whereInputs: IWhereEntityInput,
    numberConvertibleFields?: NumberConvertibleFields,
  ): Prisma.Sql[] {
    const whereClauses: Prisma.Sql[] = [];

    Object.entries(whereInputs).forEach(([field, whereInput]) => {
      if (!whereInput) {
        return;
      }

      const { value, operation } = whereInput;
      const parsedField = snakeCase(field);
      const parsedValue = this.parseValue(
        value,
        field,
        numberConvertibleFields,
      );

      switch (operation) {
        case WhereOperator.IS:
        case WhereOperator.EQUALS:
          whereClauses.push(
            Prisma.sql`${Prisma.raw(parsedField)} = ${parsedValue}`,
          );
          break;

        case WhereOperator.CONTAINS:
          whereClauses.push(
            Prisma.sql`${Prisma.raw(parsedField)} ILIKE ${"%" + parsedValue + "%"}`,
          );
          break;

        case WhereOperator.GTE:
          whereClauses.push(
            Prisma.sql`${Prisma.raw(parsedField)} >= ${parsedValue}`,
          );
          break;

        case WhereOperator.LTE:
          whereClauses.push(
            Prisma.sql`${Prisma.raw(parsedField)} <= ${parsedValue}`,
          );
          break;

        default:
          throw new Error(
            `Unsupported operation: ${operation as string} for field: ${field}`,
          );
      }
    });

    return whereClauses;
  }

  mapOrderInput(orderInput?: OrderByInput): Record<string, unknown> {
    if (!orderInput) {
      return {};
    }
    const condition: Record<string, unknown> = {};
    condition[orderInput.field] = orderInput.order;

    return condition;
  }

  private parseValue(
    rawValue: string | number,
    field: string,
    numberConvertibleFields?: NumberConvertibleFields,
  ) {
    if (!numberConvertibleFields) {
      return rawValue;
    }
    if (numberConvertibleFields.includes(field)) {
      const numericValue = Number(rawValue);
      if (isNaN(numericValue)) {
        throw new Error(
          `Invalid value provided ${rawValue}: value must be a number`,
        );
      }
      return numericValue;
    }
    return rawValue;
  }
}
