import { Injectable } from "@nestjs/common";
import { PaginationMeta } from "../dto/pagination-meta.dto";
import { PaginationParameters } from "../dto/pagination-parameters.dto";
import { PaginationInput } from "../dto/pagination.input";

@Injectable()
export class PaginationService {
  getPaginationParams(pagination: PaginationInput): PaginationParameters {
    const { page, pageSize } = pagination;

    const skip = page * pageSize;
    const take = pageSize;

    return { skip, take };
  }

  getPaginationMeta(
    pagination: PaginationInput,
    totalCount: number,
  ): PaginationMeta {
    const { page, pageSize } = pagination;

    const totalPages = Math.ceil(totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      totalCount,
      currentPage: page,
      totalPages,
      hasNextPage,
      hasPreviousPage,
    };
  }
}
