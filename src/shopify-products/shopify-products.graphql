type ShopifyMedia {
    url: String!
    type: MediaType!
    alt: String
}

type ShopifyProduct implements ShopifyNode & HasTimestamps & HasShopifyTimestamps & HasSyncTimestamp {
    shopifyId: BigInt!
    title: String!
    handle: String!
    createdAt: DateTime!
    updatedAt: DateTime!
    shopifyCreatedAt: DateTime!
    shopifyUpdatedAt: DateTime!
    lastSyncedAt: DateTime!
}

enum ShopifyProductStatus {
    ACTIVE
    DRAFT
    ARCHIVED
}
