import { Type } from "class-transformer";
import {
  IsString,
  IsOptional,
  IsEnum,
  IsInt,
  IsUrl,
  Min,
  MaxLength,
} from "class-validator";
import { MediaType, ShopifyMedia } from "types/prisma-types";

export class ShopifyMediaInput implements ShopifyMedia {
  @IsString()
  @MaxLength(255)
  id: string;

  @IsUrl()
  url: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsEnum(MediaType)
  type: MediaType;

  @IsInt()
  @Min(0)
  @Type(() => Number)
  position: number;

  @IsOptional()
  @IsString()
  shopifyId?: string;
}
