import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
  ValidateNested,
} from "class-validator";
import { ShopifyProductStatus } from "types/prisma-types";
import { IsBigInt } from "../../common/validators/bigint.validator";
import { ShopifyMediaInput } from "./shopify-media.input";
import { ShopifySeoInput } from "./shopify-seo.input";

export class CreateShopifyProductInput {
  @IsBigInt()
  @Transform(({ value }) =>
    typeof value === "string" ||
    typeof value === "number" ||
    typeof value === "bigint"
      ? BigInt(value)
      : undefined,
  )
  shopifyId: bigint;

  @IsBigInt()
  @Transform(({ value }) =>
    typeof value === "string" ||
    typeof value === "number" ||
    typeof value === "bigint"
      ? BigInt(value)
      : undefined,
  )
  shopId: bigint;

  @IsString()
  @MinLength(1)
  @MaxLength(255)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  title: string;

  @IsString()
  @MinLength(1)
  @MaxLength(255)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  handle: string;

  @IsString()
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  productType: string;

  @IsString()
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  vendor: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  description?: string;

  @IsEnum(ShopifyProductStatus, {
    message: "Status must be ACTIVE, DRAFT, or ARCHIVED",
  })
  status: ShopifyProductStatus;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(250)
  @Transform(({ value }) =>
    Array.isArray(value)
      ? value
          .filter((tag) => typeof tag === "string")
          .map((tag) => tag.toString().trim())
          .filter(Boolean)
      : [],
  )
  tags?: string[];

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === "true") return true;
    if (value === "false") return false;
    return Boolean(value);
  })
  isGiftCard?: boolean;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  templateSuffix?: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(20)
  @ValidateNested({ each: true })
  @Type(() => ShopifyMediaInput)
  media?: ShopifyMediaInput[];

  @ValidateNested()
  @Type(() => ShopifySeoInput)
  seo: ShopifySeoInput;

  @IsOptional()
  @IsDateString()
  shopifyPublishedAt?: string;

  @IsOptional()
  @IsDateString()
  shopifyCreatedAt?: string;

  @IsOptional()
  @IsDateString()
  shopifyUpdatedAt?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  productId?: number;
}
