import { Transform } from "class-transformer";
import { IsOptional, IsString, <PERSON><PERSON>eng<PERSON> } from "class-validator";
import { ShopifySeo } from "types/prisma-types";

export class ShopifySeoInput implements ShopifySeo {
  @IsOptional()
  @IsString()
  @MaxLength(70)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  title?: string;

  @IsOptional()
  @IsString()
  @MaxLength(320)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  description?: string;
}
