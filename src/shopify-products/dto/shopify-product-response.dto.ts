import { Transform, Type } from "class-transformer";
import {
  ShopifyMedia,
  ShopifyProductStatus,
  ShopifySeo,
} from "types/prisma-types";

interface IShopifyProductSource {
  shopifyVariants?: unknown[];
}

function isShopifyProductSource(obj: unknown): obj is IShopifyProductSource {
  return obj !== null && typeof obj === "object";
}

export class ShopifyProductResponseDto {
  shopifyId: bigint;
  shopId: bigint;

  title: string;
  handle: string;
  productType: string;
  vendor: string;
  description?: string;
  status: ShopifyProductStatus;
  tags: string[];
  isGiftCard: boolean;
  templateSuffix?: string;

  @Type(() => Object)
  media: ShopifyMedia[];

  @Type(() => Object)
  seo: ShopifySeo;

  createdAt: Date;
  updatedAt: Date;
  lastSyncedAt?: Date;
  shopifyPublishedAt?: Date;
  shopifyCreatedAt?: Date;
  shopifyUpdatedAt?: Date;
  productId?: number;

  // Computed fields
  @Transform(({ obj }) =>
    isShopifyProductSource(obj) ? (obj.shopifyVariants?.length ?? 0) : 0,
  )
  variantCount: number;

  // Relations
  product?: unknown;
  shopifyVariants?: unknown[];
}
