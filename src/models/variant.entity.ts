import { Expose, Type } from "class-transformer";
import BaseEntity from "./base.entity";
import { Product } from "./shopify-product.entity";
import { VariantMedia } from "./varient-media.entity";

export class Variant extends BaseEntity {
  @Expose()
  sku: string;

  @Expose()
  variantName: string;

  @Expose()
  @Type(() => VariantMedia)
  media: VariantMedia[];

  @Expose()
  productId: bigint;

  @Expose()
  @Type(() => Product)
  product: Product;
}
