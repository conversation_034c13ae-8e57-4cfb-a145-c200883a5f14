import { Expose, Type } from "class-transformer";
import BaseEntity from "./base.entity";
import { Variant } from "./shopify-variant.entity";

export enum ProductStatus {
  Active = "ACTIVE",
  Draft = "DRAFT",
  Archived = "ARCHIVED",
}

export class Product extends BaseEntity {
  @Expose()
  shopId: bigint;

  @Expose()
  title: string;

  @Expose()
  handle: string;

  @Expose()
  vendor: string;

  @Expose()
  productType: string;

  @Expose()
  status: ProductStatus;

  @Expose()
  tags: string[];

  @Expose()
  isGiftCard: boolean;

  @Expose()
  description?: string;

  @Expose()
  seoTitle?: string;

  @Expose()
  seoDescription?: string;

  @Expose()
  templateSuffix?: string;

  @Expose()
  @Type(() => Date)
  publishedAt?: Date;

  @Expose()
  @Type(() => Variant)
  variants: Variant[];
}
