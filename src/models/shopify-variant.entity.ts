import { Expose, Type } from "class-transformer";
import BaseEntity from "./base.entity";
import { Product } from "./shopify-product.entity";

export class Variant extends BaseEntity {
  @Expose()
  title: string;

  @Expose()
  displayName: string;

  @Expose()
  price: number;

  @Expose()
  compareAtPrice?: number;

  @Expose()
  position: number;

  @Expose()
  sku?: string;

  @Expose()
  taxable: boolean;

  @Expose()
  barcode?: string;

  @Expose()
  productId: bigint;

  @Expose()
  @Type(() => Product)
  product: Product;
}
