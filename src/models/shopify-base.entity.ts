import { Expose, Type } from "class-transformer";
import { IWithTimestamps } from "./base.entity";

export interface IShopifyBaseEntity {
  shopifyId: bigint;
}

export interface IWithShopifyTimestamps {
  shopifyCreatedAt?: Date;

  shopifyUpdatedAt?: Date;
}

export interface IHasSyncTimestamp {
  lastSyncedAt?: Date;
}

export default class ShopifyBaseEntity
  implements
    IShopifyBaseEntity,
    IWithTimestamps,
    IWithShopifyTimestamps,
    IHasSyncTimestamp
{
  @Expose()
  shopifyId: bigint;

  @Expose()
  @Type(() => Date)
  createdAt: Date;

  @Expose()
  @Type(() => Date)
  updatedAt: Date;

  @Expose()
  @Type(() => Date)
  shopifyCreatedAt?: Date;

  @Expose()
  @Type(() => Date)
  shopifyUpdatedAt?: Date;

  @Expose()
  @Type(() => Date)
  lastSyncedAt?: Date;
}
