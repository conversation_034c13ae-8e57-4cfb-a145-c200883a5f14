import { registerAs } from "@nestjs/config";
import { IAWSConfig } from "../interfaces/aws.interface";

export default registerAs(
  "aws",
  (): IAWSConfig => ({
    productsQueueUrl: process.env.AWS_SQS_PRODUCTS_QUEUE_URL,
    fileUploadQueueUrl: process.env.AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL,
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    fileUploadBucket: process.env.AWS_S3_PRODUCT_FILE_UPLOAD_BUCKET_NAME,
  }),
);
