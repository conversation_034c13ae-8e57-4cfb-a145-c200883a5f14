import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class AppEnvironmentVariables {
  @IsNumber()
  @Min(0)
  @Max(65535)
  @IsOptional()
  PORT?: number;

  @IsString()
  @IsOptional()
  HOST?: string;

  @IsString()
  @IsOptional()
  VERSION?: string;

  @IsString()
  @IsOptional()
  GLOBAL_PREFIX?: string;
}
