import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class MicroserviceEnvironmentVariables {
  @IsNumber()
  @Min(0)
  @Max(65535)
  @IsOptional()
  MICROSERVICE_PORT?: number;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  SERVICE_NAME?: string;

  @IsString()
  @IsOptional()
  SERVICE_DISCOVERY_NAMESPACE?: string;

  @IsString()
  @IsOptional()
  SHOP_MICROSERVICE_HOST?: string;

  @IsNumber()
  @Min(0)
  @Max(65535)
  @IsOptional()
  SHOP_MICROSERVICE_PORT?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  SHOP_SERVICE_TIMEOUT?: number;
}
