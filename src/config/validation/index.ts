import { ConfigObject } from "@nestjs/config";
import { IntersectionType } from "@nestjs/mapped-types";
import { plainToInstance } from "class-transformer";
import { validateSync } from "class-validator";
import { AppEnvironmentVariables } from "./app.validator";
import { AwsEnvironmentVariables } from "./aws.validator";
import { DatabaseEnvironmentVariables } from "./database.validator";
import { GraphQLEnvironmentVariables } from "./graphql.validator";
import { MicroserviceEnvironmentVariables } from "./microservice.validator";

export class EnvironmentVariables extends IntersectionType(
  AppEnvironmentVariables,
  DatabaseEnvironmentVariables,
  GraphQLEnvironmentVariables,
  AwsEnvironmentVariables,
  MicroserviceEnvironmentVariables,
) {}

export function validate(config: ConfigObject) {
  const instance = plainToInstance(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });

  const validationErrors = validateSync(instance, {
    skipMissingProperties: false,
    whitelist: true,
    forbidNonWhitelisted: false,
  });

  if (validationErrors.length > 0) {
    const errors = validationErrors.map((error) => {
      const constraints = Object.values(error.constraints || {}).join(", ");
      return `${error.property}: ${constraints}`;
    });

    throw new Error(`Configuration validation failed:\n${errors.join("\n")}`);
  }

  return instance;
}
