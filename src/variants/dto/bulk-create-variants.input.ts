import { Type } from "class-transformer";
import { ArrayMaxSize, IsArray, ValidateNested } from "class-validator";
import { CreateVariantInput } from "./create-variant.input";

export class BulkCreateVariantsInput {
  @IsArray()
  @ArrayMaxSize(100, {
    message: "Cannot create more than 100 variants at once",
  })
  @ValidateNested({ each: true })
  @Type(() => CreateVariantInput)
  variants: CreateVariantInput[];
}
