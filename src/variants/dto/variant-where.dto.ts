import { Type } from "class-transformer";
import { IsOptional, ValidateNested } from "class-validator";
import { IWhereEntityInput, WhereInput } from "../../common/dto/where.input";

export class VariantWhereDto implements IWhereEntityInput {
  [key: string]: WhereInput | undefined;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  id?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  sku?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  variantName?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  productId?: WhereInput;
}
