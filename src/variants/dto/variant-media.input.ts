import { Type } from "class-transformer";
import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
  Min,
} from "class-validator";
import { MediaType, VariantMedia } from "types/prisma-types";

export class VariantMediaInput implements VariantMedia {
  @IsUrl({}, { message: "Must be a valid URL" })
  url: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsEnum(MediaType, { message: "Type must be image or video" })
  type: MediaType;

  @IsInt()
  @Min(0)
  @Type(() => Number)
  position: number;
}
