import { Transform, Type } from "class-transformer";
import { VariantMedia } from "src/models/varient-media.entity";
import { VariantOptions } from "types/prisma-types";

interface IVariantSource {
  product?: unknown;
  shopifyVariants?: unknown[];
}

function isVariantSource(obj: unknown): obj is IVariantSource {
  return obj !== null && typeof obj === "object";
}

export class VariantResponseDto {
  id: number;
  sku: string;
  variantName: string;
  productId: number;

  @Type(() => Object)
  options?: VariantOptions;

  @Type(() => VariantMedia)
  media: VariantMedia[];

  createdAt: Date;
  updatedAt: Date;

  // Computed fields
  @Transform(({ obj }) =>
    isVariantSource(obj) ? (obj.shopifyVariants?.length ?? 0) : 0,
  )
  shopifyVariantCount: number;

  // Relations (populated by field resolvers)
  product?: unknown;
  shopifyVariants?: unknown[];
}
