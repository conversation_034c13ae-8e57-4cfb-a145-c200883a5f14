import { Transform } from "class-transformer";
import { <PERSON>Optional, IsString, <PERSON><PERSON>eng<PERSON> } from "class-validator";

export class VariantOptionsInput {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  size?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  flavor?: string;

  // Allow additional dynamic properties
  [key: string]: string | undefined;
}
