import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsInt,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON>th,
  ValidateNested,
} from "class-validator";
import { VariantMediaInput } from "./variant-media.input";

export class CreateVariantInput {
  @IsString()
  @MinLength(1)
  @MaxLength(16, { message: "SKU cannot exceed 16 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.toUpperCase().trim() : undefined,
  )
  sku: string;

  @IsString()
  @MinLength(1, { message: "Variant name is required" })
  @MaxLength(255, { message: "Variant name cannot exceed 255 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  variantName: string;

  @IsInt()
  productId: number;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(20, { message: "Cannot have more than 20 media items" })
  @ValidateNested({ each: true })
  @Type(() => VariantMediaInput)
  media?: VariantMediaInput[];
}
