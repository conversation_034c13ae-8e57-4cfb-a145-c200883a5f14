import { Module } from "@nestjs/common";
import { CommonModule } from "../common/common.module";
import { VariantRepository } from "./repositories/variant.repository";
import { VariantsService } from "./services/variants.service";
import { VariantsResolver } from "./variants.resolver";

@Module({
  imports: [CommonModule],
  providers: [VariantsResolver, VariantsService, VariantRepository],
  exports: [VariantsService, VariantRepository],
})
export class VariantsModule {}
