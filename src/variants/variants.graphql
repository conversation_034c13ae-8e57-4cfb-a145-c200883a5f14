type VariantMedia {
  url: String!
  type: MediaType!
  alt: String
  position: Int
}

type Variant implements Node & HasTimestamps {
  id: Int!
  sku: String!
  variantName: String!
  productId: Int!
  #product: Product!
  options: JSON
  media: [VariantMedia!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

extend type Product {
  variants: [Variant!]
}

extend type Query {
  variants(
    pagination: PaginationInput!
    where: VariantWhereInput
    quickSearchQuery: String
  ): VariantConnection!

  variant(id: Int!): Variant
}

type VariantConnection implements PaginationConnection {
  data: [Variant!]!
  meta: PaginationMeta!
}

input CreateVariantInput {
  sku: String!
  variantName: String!
  productId: Int!
}

input VariantWhereInput {
  id: WhereInput
  sku: WhereInput
  variantName: WhereInput
  productId: WhereInput
}
