import {
  isNullOrUndefined,
  isObject,
  isStringOrNumber,
  isTypedArray,
} from "../../common/types/type-guards";
import {
  isVariantMessage,
  IVariantMessage,
} from "../../variants/types/IVariantMessage";

export interface IProductDataMessage {
  id: string | number;
  title?: string | null;
  variants: IVariantMessage[];
}

// New message type for Excel-parsed products
export interface IExcelProductMessage {
  productName: string;
  color: string;
  variants: {
    variantName: string;
    sku: string;
    size: string;
  }[];
  sizes: string[];
  shopKey: string;
  action: "PROCESS_EXCEL_PRODUCT";
}

export function isProductDataMessage(
  value: unknown,
): value is IProductDataMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "id" in value &&
    isStringOrNumber(value.id) &&
    (!("title" in value) ||
      isNullOrUndefined(value.title) ||
      typeof value.title === "string") &&
    "variants" in value &&
    isTypedArray(value.variants, isVariantMessage)
  );
}

export function assertIsProductDataMessage(
  value: unknown,
): asserts value is IProductDataMessage {
  if (!isProductDataMessage(value)) {
    throw new Error(
      "Invalid product data structure: expected object with id, optional title, and variants array",
    );
  }
}

export function isExcelProductMessage(
  value: unknown,
): value is IExcelProductMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "productName" in value &&
    typeof value.productName === "string" &&
    "color" in value &&
    typeof value.color === "string" &&
    "variants" in value &&
    Array.isArray(value.variants) &&
    "sizes" in value &&
    Array.isArray(value.sizes) &&
    "shopKey" in value &&
    typeof value.shopKey === "string" &&
    "action" in value &&
    value.action === "PROCESS_EXCEL_PRODUCT"
  );
}

export function assertIsExcelProductMessage(
  value: unknown,
): asserts value is IExcelProductMessage {
  if (!isExcelProductMessage(value)) {
    throw new Error(
      "Invalid Excel product message structure: expected object with productName, color, variants, sizes, shopKey, and action",
    );
  }
}
