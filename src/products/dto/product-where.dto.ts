import { Type } from "class-transformer";
import { IsOptional, ValidateNested } from "class-validator";
import { IWhereEntityInput, WhereInput } from "../../common/dto/where.input";

export class ProductWhereDto implements IWhereEntityInput {
  [key: string]: WhereInput | undefined;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  id?: WhereInput;

  @IsOptional()
  @ValidateNested()
  @Type(() => WhereInput)
  productName?: WhereInput;
}
