import { Type } from "class-transformer";
import { IsArray, ArrayMaxSize, ValidateNested } from "class-validator";
import { CreateProductInput } from "./create-product.input";

export class BulkCreateProductsInput {
  @IsArray()
  @ArrayMaxSize(50, { message: "Cannot create more than 50 products at once" })
  @ValidateNested({ each: true })
  @Type(() => CreateProductInput)
  products: CreateProductInput[];
}
