import { Transform, Type } from "class-transformer";
import { ProductMedia } from "src/models/product-media.entity";
import { VariantResponseDto } from "../../variants/dto/variant-response.dto";

interface IProductSource {
  variants?: VariantResponseDto[];
  shopifyProducts?: unknown[];
}

function isProductSource(obj: unknown): obj is IProductSource {
  return obj !== null && typeof obj === "object";
}

export class ProductResponseDto {
  id: number;
  productName: string;

  @Type(() => ProductMedia)
  media: ProductMedia[];

  createdAt: Date;
  updatedAt: Date;

  // Computed fields
  @Transform(({ obj }) =>
    isProductSource(obj) ? (obj.variants?.length ?? 0) : 0,
  )
  variantCount: number;

  @Transform(({ obj }) =>
    isProductSource(obj) ? (obj.shopifyProducts?.length ?? 0) : 0,
  )
  shopifyProductCount: number;

  // Relations (populated by field resolvers)
  variants?: VariantResponseDto[];
  shopifyProducts?: unknown[];
}
