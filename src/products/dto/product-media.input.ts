import {
  Is<PERSON>num,
  IsOptional,
  IsString,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";
import { ProductMedia, MediaType } from "types/prisma-types";

export class ProductMediaInput implements ProductMedia {
  @IsUrl({}, { message: "Must be a valid URL" })
  url: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsEnum(MediaType, { message: "Type must be image or video" })
  type: MediaType;
}
