import { Transform, Type } from "class-transformer";
import {
  ArrayMaxSize,
  IsArray,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  <PERSON><PERSON><PERSON>th,
  ValidateNested,
} from "class-validator";
import { ProductMediaInput } from "./product-media.input";

export class CreateProductInput {
  @IsString()
  @MinLength(1, { message: "Product name is required" })
  @MaxLength(255, { message: "Product name cannot exceed 255 characters" })
  @Transform(({ value }) =>
    typeof value === "string" ? value.trim() : undefined,
  )
  productName: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(20, { message: "Cannot have more than 20 media items" })
  @ValidateNested({ each: true })
  @Type(() => ProductMediaInput)
  media?: ProductMediaInput[];
}
