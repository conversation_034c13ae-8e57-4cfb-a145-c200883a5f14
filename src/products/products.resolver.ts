import { UsePipes, ValidationPipe } from "@nestjs/common";
import { <PERSON>rgs, Parent, Query, ResolveField, Resolver } from "@nestjs/graphql";
import { WhereOperator } from "src/common/types/where-operator";
import { Product } from "src/models/product.entity";
import { PaginationInput } from "../common/dto/pagination.input";
import { VariantsService } from "../variants/services/variants.service";
import { ProductWhereDto } from "./dto/product-where.dto";
import { ProductsService } from "./services/products.service";

@Resolver("Product")
@UsePipes(new ValidationPipe({ transform: true }))
export class ProductsResolver {
  // private readonly logger = new Logger(ProductsResolver.name);

  constructor(
    private readonly productService: ProductsService,
    private readonly variantService: VariantsService,
    // private readonly shopClient: ShopService,
  ) {}

  @Query("products")
  async findAll(
    @Args("pagination") pagination: PaginationInput,
    @Args("where") where?: ProductWhereDto,
    @Args("quickSearchQuery") quickSearchQuery?: string,
  ) {
    return this.productService.findAllPaginated(
      pagination,
      where,
      quickSearchQuery,
    );
  }

  @Query("product")
  async findOne(@Args("id") id: number) {
    return this.productService.findOne(id);
  }

  @ResolveField("variants")
  getVariants(@Parent() { id }: Product) {
    return this.variantService.findAll({
      productId: {
        value: "" + id,
        operation: WhereOperator.EQUALS,
      },
    });
  }
}
