type ProductMedia {
  url: String!
  type: MediaType!
  alt: String
}

type Product implements Node & HasTimestamps {
  id: Int!
  productName: String!
  media: [ProductMedia]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ProductConnection implements PaginationConnection {
  data: [Product!]!
  meta: PaginationMeta!
}

extend type Query {
  products(
    pagination: PaginationInput!
    where: ProductWhereInput
    quickSearchQuery: String
  ): ProductConnection!

  product(id: Int!): Product
}

input ProductWhereInput {
  id: WhereInput
  productName: WhereInput
}

enum MediaType {
  IMAGE
  VIDEO
  EXTERNAL_VIDEO
  MODEL_3D
}
