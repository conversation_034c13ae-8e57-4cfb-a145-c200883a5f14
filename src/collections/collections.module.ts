import { Module } from "@nestjs/common";
import { CommonModule } from "src/common/common.module";
import { CollectionsResolver } from "./collections.resolver";
import { CollectionRepository } from "./repositories/collection.repository";
import { CollectionsService } from "./services/collections.service";

@Module({
  imports: [CommonModule],
  providers: [CollectionsResolver, CollectionsService, CollectionRepository],
})
export class CollectionsModule {}
