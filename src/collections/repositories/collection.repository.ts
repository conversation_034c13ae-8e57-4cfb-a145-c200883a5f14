import { Injectable } from "@nestjs/common";
import { plainToInstance } from "class-transformer";
import { PaginationParameters } from "src/common/dto/pagination-parameters.dto";
import { PrismaService } from "src/common/services/prisma.service";
import { QueryMapperService } from "src/common/services/query-mapper.service";
import { Collection } from "src/models/collection.entiy";
import { CollectionUniqueInput } from "../dto/collection-unique.input";
import { CollectionWhereInput } from "../dto/collection-where.input";
import { CreateCollectionInput } from "../dto/create-collection.input";

@Injectable()
export class CollectionRepository {
  constructor(
    private readonly queryMapperService: QueryMapperService,
    private readonly prisma: PrismaService,
  ) {}

  async findAll(whereInput?: CollectionWhereInput): Promise<Collection[]> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput)
      : undefined;

    const collections = await this.prisma.collection.findMany({
      where,
    });

    return plainToInstance(Collection, collections);
  }

  async findAllPaginated(
    pagination: PaginationParameters,
    whereInput?: CollectionWhereInput,
  ): Promise<{ collections: Collection[]; totalCount: number }> {
    const where = whereInput
      ? this.queryMapperService.mapWhereInput(whereInput)
      : undefined;

    const query = {
      where,
    };
    const [totalCount, collections] = await Promise.all([
      this.prisma.collection.count(query),
      this.prisma.collection.findMany({
        ...query,
        ...pagination,
        orderBy: { id: "asc" },
      }),
    ]);
    return {
      collections: plainToInstance(Collection, collections),
      totalCount,
    };
  }

  async findOne(where: CollectionUniqueInput): Promise<Collection | null> {
    const collection = await this.prisma.collection.findUnique({
      where,
    });
    return collection ? plainToInstance(Collection, collection) : null;
  }

  async remove(id: number): Promise<Collection | null> {
    const removed = await this.prisma.collection.delete({
      where: { id },
    });
    return plainToInstance(Collection, removed);
  }

  async create(input: CreateCollectionInput): Promise<Collection | null> {
    const created = await this.prisma.collection.create({
      data: {
        name: input.name,
        tag: input.tag,
      },
    });
    return plainToInstance(Collection, created);
  }
}
