input CollectionWhereInput {
  id: WhereInput
  name: WhereInput
  tag: WhereInput
}

input CreateCollectionInput {
  name: String!
  tag: String!
}

extend type Mutation {
  createCollection(createCollectionInput: CreateCollectionInput!): Collection
  removeCollection(id: Int!): Collection
}

input CollectionWhereUniqueInput {
  id: Int!
}

extend type Query {
  collections(
    pagination: PaginationInput!
    where: CollectionWhereInput
  ): CollectionConnection!

  collection(where: CollectionWhereUniqueInput!): Collection
}

type Collection implements Node & HasTimestamps {
  id: Int!
  name: String!
  tag: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type CollectionConnection implements PaginationConnection {
  data: [Collection!]!
  meta: PaginationMeta!
}
