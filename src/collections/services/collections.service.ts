import { Injectable } from "@nestjs/common";
import { PaginationInput } from "src/common/dto/pagination.input";
import { PaginationService } from "src/common/services/pagination.service";
import { CollectionUniqueInput } from "../dto/collection-unique.input";
import { CollectionWhereInput } from "../dto/collection-where.input";
import { CreateCollectionInput } from "../dto/create-collection.input";
import { PaginatedCollectionsResponse } from "../dto/paginated-collections.dto";
import { CollectionRepository } from "../repositories/collection.repository";

@Injectable()
export class CollectionsService {
  constructor(
    private readonly collectionRepository: CollectionRepository,
    private readonly paginationService: PaginationService,
  ) {}

  async findOne(where: CollectionUniqueInput) {
    return await this.collectionRepository.findOne(where);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput: CollectionWhereInput,
  ): Promise<PaginatedCollectionsResponse> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, collections: data } =
      await this.collectionRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  remove(id: number) {
    return this.collectionRepository.remove(id);
  }

  async create(input: CreateCollectionInput) {
    return this.collectionRepository.create(input);
  }
}
