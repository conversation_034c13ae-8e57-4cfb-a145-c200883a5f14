import { Injectable, PipeTransform } from "@nestjs/common";
import * as XLSX from "xlsx";

export interface IParsedVariant {
  variantName: string;
  sku: string;
  size: string;
}

export interface IParsedProduct {
  productName: string;
  color: string;
  variants: IParsedVariant[];
  sizes: string[];
}

// Column indices based on provided order (0-based):
const COL_STYLE_NAME = 1;
const COL_COLOR = 3;
const COL_SKU = 5;
const COL_PRODUCT = 4;

@Injectable()
export class ExcelParserPipe
  implements PipeTransform<Buffer, IParsedProduct[]>
{
  // Transform an Excel Buffer to structured products
  transform(buffer: Buffer): IParsedProduct[] {
    const wb = XLSX.read(buffer, { type: "buffer" });
    const sheetName = wb.SheetNames[0];
    if (sheetName === "") return [];

    const ws = wb.Sheets[sheetName];
    const rows: unknown[][] = XLSX.utils.sheet_to_json(ws, {
      header: 1,
      raw: false,
      blankrows: false,
      defval: "",
    });
    const dataRows = rows.slice(3) as (string | number)[][];

    const map = new Map<string, IParsedProduct>();

    dataRows.forEach((row) => {
      if (row.length === 0) return;

      const styleName = String(row[COL_STYLE_NAME] ?? "").trim();
      const color = String(row[COL_COLOR] ?? "").trim();
      const sku = String(row[COL_SKU] ?? "").trim();
      const variantName = String(row[COL_PRODUCT] ?? "").trim();

      if (
        (styleName === "" && color === "") ||
        (sku === "" && variantName === "")
      ) {
        return;
      }

      const productName = `${styleName} | ${color}`.trim();

      if (!map.has(productName)) {
        map.set(productName, {
          productName,
          variants: [],
          color,
          sizes: [],
        });
      }

      const product = map.get(productName)!;
      const size = extractSizeFromVariantName(variantName);
      if (size !== "" && !product.sizes.includes(size)) {
        product.sizes.push(size);
      }
      product.variants.push({ variantName, sku, size });
    });

    return Array.from(map.values());
  }
}

function extractSizeFromVariantName(variantName: string): string {
  const SIZE_REGEX =
    /\b(Extra Small|Small|Medium|Large|Extra Large|Double Extra Large)\b/i;
  const match = variantName.match(SIZE_REGEX)?.[1];

  return match ?? "";
}
