export interface IProductFileMessage {
  fileId: number;
  s3Key: string;
  fileName: string;
  action: "PROCESS_EXCEL_PRODUCT_FILE";
}

export function isProductFileMessage(
  value: unknown,
): value is IProductFileMessage {
  return (
    typeof value === "object" &&
    value !== null &&
    "fileId" in value &&
    typeof value.fileId === "number" &&
    "s3Key" in value &&
    typeof value.s3Key === "string" &&
    "fileName" in value &&
    typeof value.fileName === "string" &&
    "action" in value &&
    value.action === "PROCESS_EXCEL_PRODUCT_FILE"
  );
}
