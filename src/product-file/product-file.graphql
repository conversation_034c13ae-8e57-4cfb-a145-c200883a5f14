enum ProductFileStatus {
  UPLOADED
  PROCESSING
  FAILED
  COMPLETED
}

type ProductFile {
  id: Int!
  name: String!
  s3Key: String!
  s3Url: String!
  status: ProductFileStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type FileUploadResponse {
  success: Boolean!
  message: String!
  file: ProductFile
}


type Mutation {
  uploadProductsExcelFile(file: File!): FileUploadResponse!
}

type Query {
  productFiles: [ProductFile!]!
  productFile(id: Int!): ProductFile
}
