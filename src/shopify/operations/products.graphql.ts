export const GET_PRODUCTS = `#graphql
query GetProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
        edges {
            node {
                id
                title
                handle
                variants(first: 100) {
                    edges {
                        node {
                            id
                            title
                            price
                            compareAtPrice
                            sku
                        }
                    }
                }
            }
        }
        pageInfo {
            hasNextPage
            endCursor
        }
    }
}
`;

export const BULK_SET_PRODUCTS = `#graphql
mutation BulkSetProducts($productSet: ProductSetInput!, $synchronous: Boolean!) {
    productSet(synchronous: $synchronous, input: $productSet) {
        product { id }
        productSetOperation { id status userErrors { code field message } }
        userErrors { code field message }
    }
}
`;
