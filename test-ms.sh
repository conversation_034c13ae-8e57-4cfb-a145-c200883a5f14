#!/bin/bash
# Get the first running task and connect
TASK_ARN=$(aws ecs list-tasks \
  --cluster be-product-master-cluster \
  --service-name be-product-master-service \
  --region eu-central-1 \
  --query 'taskArns[0]' \
  --output text)

aws ecs execute-command \
  --cluster be-product-master-cluster \
  --task $TASK_ARN \
  --container be-product-master \
  --command "/bin/bash" \
  --interactive \
  --region eu-central-1