# Product Master

Microservice for managing Shopify products across Shopify shops.

## Project setup

Log into private NPM with your GitHub username and personal access token as the password.

```sh
pnpm login --scope=@sw-ecom360 --registry=https://npm.pkg.github.com
```

dependencies

```sh
pnpm install
```

.env

```sh
pnpm run env
```

### Git Hooks (<PERSON><PERSON>)

This project uses <PERSON>sky for Git hooks to ensure code quality. The pre-commit hook will automatically:

- Run ESLint to check for code style and potential issues
- Run the build process to ensure everything compiles

The hooks are automatically installed when you run `pnpm install` (via the `prepare` script).

To manually run the pre-commit checks:

```sh
.husky/pre-commit
```

To skip hooks temporarily (not recommended):

```sh
git commit --no-verify -m "your message"
```

### Configure Shopify Shops

Configure Shopify shops in `config/shops.yaml`:

```yaml
shops:
  dev1:
    domain: dev1.myshopify.com
  dev2:
    domain: dev2.myshopify.com
```

Set shop access tokens in `.env` file. Environment variable names need to match the pattern `SHOPIFY_ACCESS_TOKEN_<SHOP_KEY_UPPERCASE>`:

```dotenv
# Example .env entries
SHOPIFY_ACCESS_TOKEN_DEV1=shpat_xxxxxxxxxxxxxxxxxxxx
SHOPIFY_ACCESS_TOKEN_DEV2=shpat_xxxxxxxxxxxxxxxxxxxx
```

### Generate the Shopify API code

The generated code provides TypeScript types and GraphQL operations for interacting with the Shopify API.

```bash
pnpm run shopify:types # or pnpm run graphql-codegen
```

Watch mode

```bash
pnpm run graphql-codegen --watch
```

This will generate TypeScript types and API clients in the `generated/shopify/types` directory based on your GraphQL queries and the Shopify API schema.

## Compile and run the project

development

```sh
pnpm run docker:up
```

```sh
pnpm run start
```

watch mode

```sh
pnpm run start:dev
```

production mode

```sh
pnpm run start:prod
```

build

```sh
pnpm run build
```

## Run tests

unit tests

```sh
pnpm run test
```

e2e tests

```sh
pnpm run test:e2e
```

test coverage

```sh
pnpm run test:cov
```

## Deployment

### Infrastructure

The application uses AWS CloudFormation for infrastructure as code. The infrastructure includes:

- **ECS Fargate** for container orchestration
- **RDS PostgreSQL** for the database (private subnets only)
- **Application Load Balancer** for routing
- **CloudWatch** for logging and monitoring
- **Secrets Manager** for secure credential storage

### Deploy to AWS

1. **Prerequisites**:
   - AWS CLI configured with appropriate permissions
   - Docker installed for building images

2. **Deploy Infrastructure**:

   ```bash
   # Deploy ECR repository first
   ./ci/scripts/cloudformation/deploy-ecr.sh eu-central-1 production

   # Deploy application infrastructure
   ./ci/scripts/cloudformation/deploy-be-product-master.sh eu-central-1 production latest
   ```

3. **Run Database Migrations**:
   ```bash
   # Migrations run as ECS tasks in the same private network as the application
   ./ci/scripts/run-migrations.sh eu-central-1 production
   ```

### Database Security

The RDS instance is configured for maximum security:

- **Private subnets only** - No public internet access
- **Security groups** - Only ECS tasks can access the database
- **Encrypted storage** - Data encrypted at rest
- **Secrets Manager** - Database credentials stored securely

Migrations are executed using ECS Fargate tasks that run in the same private network as the application, eliminating the need for public database access or jump boxes.

### Environment Variables

The application uses the following environment variables in production:

- `DATABASE_URL` - PostgreSQL connection string (stored in AWS Secrets Manager)
- `NODE_ENV` - Set to `production`
- `PORT` - HTTP server port (3000)
- `MICROSERVICE_PORT` - Internal microservice port (3003)
- `SERVICE_DISCOVERY_NAMESPACE` - AWS Service Discovery namespace
- `SERVICE_NAME` - Service name for discovery
- `AWS_REGION` - AWS region for services
