export {};

export const MediaType = {
  IMAGE: "IMAGE",
  VIDEO: "VIDEO",
  EXTERNAL_VIDEO: "EXTERNAL_VIDEO",
  MODEL_3D: "MODEL_3D",
} as const;

export const ShopifyProductStatus = {
  ACTIVE: "ACTIVE",
  DRAFT: "DRAFT",
  ARCHIVED: "ARCHIVED",
} as const;

declare global {
  namespace PrismaJson {
    type MediaType = (typeof MediaType)[keyof typeof MediaType];

    type ProductSeo = {
      title?: string;
      description?: string;
    };

    type ProductMedia = {
      url: string;
      type: MediaType;
      alt?: string;
    };

    type VariantOptions = {
      [key: string]: string; // e.g., { size: "M", flavor: "Vanilla" }
    };

    type VariantMedia = {
      url: string;
      type: MediaType;
      alt?: string;
      position?: number;
    };

    type ShopifyMedia = {
      url: string;
      type: MediaType;
      alt?: string;
    };

    type ShopifySeo = {
      title?: string;
      description?: string;
    };

    type ShopifyProductStatus =
      (typeof ShopifyProductStatus)[keyof typeof ShopifyProductStatus];
  }
}

export type MediaType = PrismaJson.MediaType;
export type ProductSeo = PrismaJson.ProductSeo;
export type ProductMedia = PrismaJson.ProductMedia;
export type VariantOptions = PrismaJson.VariantOptions;
export type VariantMedia = PrismaJson.VariantMedia;
export type ShopifyMedia = PrismaJson.ShopifyMedia;
export type ShopifySeo = PrismaJson.ShopifySeo;
export type ShopifyProductStatus = PrismaJson.ShopifyProductStatus;
