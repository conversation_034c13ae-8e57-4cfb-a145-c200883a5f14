model ShopifyProduct {
  shopifyId BigInt @id @map("shopify_id")
  shopId    BigInt @map("shop_id")

  title       String  @db.VarChar(255)
  handle      String  @db.VarChar(255)
  productType String  @map("product_type")
  vendor      String
  description String? @db.Text

  /// [ShopifyProductStatus]
  status String

  /// [string]
  tags String[]

  isGiftCard Boolean @default(false) @map("is_gift_card")

  templateSuffix String? @map("template_suffix")

  /// [ShopifyMedia]
  media Json[] @map("media")

  /// [ShopifySeo]
  seo Json @map("seo")

  // Timestamps
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  lastSyncedAt       DateTime? @map("last_synced_at")
  shopifyPublishedAt DateTime? @map("shopify_published_at")
  shopifyCreatedAt   DateTime? @map("shopify_created_at")
  shopifyUpdatedAt   DateTime? @map("shopify_updated_at")

  // Relations
  product   Product? @relation(fields: [productId], references: [id])
  productId Int?     @map("product_id")

  shopifyVariants ShopifyVariant[]

  @@index([handle])
  @@index([productId])
  @@index([shopId])
  @@map("shopify_products")
}

model ShopifyVariant {
  shopifyId   BigInt  @id @map("shopify_id")
  title       String  @map("title") @db.VarChar(255)
  displayName String  @map("display_name")
  sku         String? @map("sku") @db.VarChar(16)
  barcode     String? @map("barcode") @db.VarChar(50)

  price          Float  @map("price")
  compareAtPrice Float? @map("compare_at_price")

  position     Int                      @map("position")
  taxable      Boolean                  @default(true) @map("taxable")
  searchVector Unsupported("tsvector")? @map("search_vector")

  /// [ShopifyMedia]
  media Json[] @map("media")

  inventoryQuantity Int?    @map("inventory_quantity")
  inventoryItemId   BigInt? @map("inventory_item_id")

  // Timestamps
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  lastSyncedAt     DateTime? @map("last_synced_at")
  shopifyCreatedAt DateTime? @map("shopify_created_at")
  shopifyUpdatedAt DateTime? @map("shopify_updated_at")

  // Relations
  shopifyProduct   ShopifyProduct @relation(fields: [shopifyProductId], references: [shopifyId], onDelete: Cascade)
  shopifyProductId BigInt         @map("shopify_product_id")

  variant   Variant? @relation(fields: [variantId], references: [id])
  variantId Int?     @map("variant_id")

  @@index([shopifyProductId])
  @@index([sku])
  @@index([position])
  @@index([variantId])
  @@index([searchVector], type: Gin)
  @@map("shopify_variants")
}
