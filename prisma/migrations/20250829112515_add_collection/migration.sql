/*
  Warnings:

  - Made the column `status` on table `product_files` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `collection_id` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."product_files" ALTER COLUMN "status" SET NOT NULL;

-- CreateTable
CREATE TABLE "public"."collections" (
  "id" SERIAL NOT NULL,
  "name" VARCHAR(255) NOT NULL,
  "tag" VARCHAR(255) NOT NULL,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "collections_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "collections_name_idx" ON "public"."collections"("name");

-- AlterTable
ALTER TABLE "public"."products" ADD COLUMN "collection_id" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "public"."products" ADD CONSTRAINT "products_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE CASCADE ON UPDATE CASCADE;
