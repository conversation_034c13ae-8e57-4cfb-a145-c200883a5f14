-- CreateTable
CREATE TABLE "products" (
    "id" SERIAL NOT NULL,
    "product_name" VARCHAR(255) NOT NULL,
    "media" JSONB[],
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "variants" (
    "id" SERIAL NOT NULL,
    "sku" VARCHAR(16) NOT NULL,
    "variant_name" VARCHAR(255) NOT NULL,
    "options" JSONB,
    "media" JSONB[],
    "search_vector" tsvector,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "product_id" INTEGER NOT NULL,

    CONSTRAINT "variants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopify_products" (
    "shopify_id" BIGINT NOT NULL,
    "shop_id" BIGINT NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "handle" VARCHAR(255) NOT NULL,
    "product_type" TEXT NOT NULL,
    "vendor" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL,
    "tags" TEXT[],
    "is_gift_card" BOOLEAN NOT NULL DEFAULT false,
    "template_suffix" TEXT,
    "media" JSONB[],
    "seo" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_synced_at" TIMESTAMP(3),
    "shopify_published_at" TIMESTAMP(3),
    "shopify_created_at" TIMESTAMP(3),
    "shopify_updated_at" TIMESTAMP(3),
    "product_id" INTEGER,

    CONSTRAINT "shopify_products_pkey" PRIMARY KEY ("shopify_id")
);

-- CreateTable
CREATE TABLE "shopify_variants" (
    "shopify_id" BIGINT NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "display_name" TEXT NOT NULL,
    "sku" VARCHAR(16),
    "barcode" VARCHAR(50),
    "price" DOUBLE PRECISION NOT NULL,
    "compare_at_price" DOUBLE PRECISION,
    "position" INTEGER NOT NULL,
    "taxable" BOOLEAN NOT NULL DEFAULT true,
    "search_vector" tsvector,
    "media" JSONB[],
    "inventory_quantity" INTEGER,
    "inventory_item_id" BIGINT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_synced_at" TIMESTAMP(3),
    "shopify_created_at" TIMESTAMP(3),
    "shopify_updated_at" TIMESTAMP(3),
    "shopify_product_id" BIGINT NOT NULL,
    "variant_id" INTEGER,

    CONSTRAINT "shopify_variants_pkey" PRIMARY KEY ("shopify_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "variants_sku_key" ON "variants"("sku");

-- CreateIndex
CREATE INDEX "variants_sku_idx" ON "variants"("sku");

-- CreateIndex
CREATE INDEX "variants_search_vector_idx" ON "variants" USING GIN ("search_vector");

-- CreateIndex
CREATE INDEX "shopify_products_handle_idx" ON "shopify_products"("handle");

-- CreateIndex
CREATE INDEX "shopify_products_product_id_idx" ON "shopify_products"("product_id");

-- CreateIndex
CREATE INDEX "shopify_products_shop_id_idx" ON "shopify_products"("shop_id");

-- CreateIndex
CREATE INDEX "shopify_variants_shopify_product_id_idx" ON "shopify_variants"("shopify_product_id");

-- CreateIndex
CREATE INDEX "shopify_variants_sku_idx" ON "shopify_variants"("sku");

-- CreateIndex
CREATE INDEX "shopify_variants_position_idx" ON "shopify_variants"("position");

-- CreateIndex
CREATE INDEX "shopify_variants_variant_id_idx" ON "shopify_variants"("variant_id");

-- CreateIndex
CREATE INDEX "shopify_variants_search_vector_idx" ON "shopify_variants" USING GIN ("search_vector");

-- AddForeignKey
ALTER TABLE "variants" ADD CONSTRAINT "variants_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_products" ADD CONSTRAINT "shopify_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_variants" ADD CONSTRAINT "shopify_variants_shopify_product_id_fkey" FOREIGN KEY ("shopify_product_id") REFERENCES "shopify_products"("shopify_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_variants" ADD CONSTRAINT "shopify_variants_variant_id_fkey" FOREIGN KEY ("variant_id") REFERENCES "variants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create a function to update the search vector
CREATE OR REPLACE FUNCTION variants_search_vector_update() RETURNS trigger AS $$
BEGIN
    NEW.search_vector = to_tsvector('english', coalesce(NEW.variant_name, '') || ' ' || coalesce(NEW.sku, ''));
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the search vector
CREATE TRIGGER variants_search_vector_update
    BEFORE INSERT OR UPDATE ON "variants"
    FOR EACH ROW EXECUTE FUNCTION variants_search_vector_update();