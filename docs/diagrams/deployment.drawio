<mxfile host="Electron" modified="2025-08-27T13:40:56.183Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.7.5 Chrome/114.0.5735.289 Electron/25.8.1 Safari/537.36" version="21.7.5" etag="Oa1TE7xi3QZPpb4D6vVg" type="device">
  <diagram id="deploy-1" name="Deployment">
    <mxGraphModel dx="1114" dy="841" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="dev" value="Developer / CI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="20" y="130" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="dockerfile" value="Dockerfile\n(pnpm build, codegen, prune)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="200" y="110" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="buildpush" value="build-and-push.sh" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="460" y="120" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="ecr" value="ECR Repository" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cdeb8b;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="660" y="110" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ecrstack" value="CloudFormation\nECR stack (ecr.yaml)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="200" y="230" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="appstack" value="CloudFormation\nApp stack (be-product-master.yaml)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="460" y="230" width="280" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ecs" value="ECS Service / Task Update" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="780" y="230" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="migrate" value="run-migrations.sh\n(prisma migrate deploy)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="200" y="350" width="260" height="70" as="geometry" />
        </mxCell>
        <mxCell id="rds" value="RDS PostgreSQL" style="shape=cylinder;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="500" y="350" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="e1" style="endArrow=classic;" parent="1" source="dev" target="dockerfile" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e2" style="endArrow=classic;" parent="1" source="dockerfile" target="buildpush" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e3" style="endArrow=classic;" parent="1" source="buildpush" target="ecr" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e4" style="endArrow=classic;" parent="1" source="dev" target="ecrstack" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e5" style="endArrow=classic;" parent="1" source="dev" target="appstack" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e6" style="endArrow=classic;" parent="1" source="appstack" target="ecs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e7" style="endArrow=classic;" parent="1" source="migrate" target="rds" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e8" style="endArrow=classic;dashed=1;" parent="1" source="ecr" target="ecs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="XRrvoEsBIDFymCDPDrOS-1" value="&lt;font style=&quot;font-size: 32px;&quot;&gt;Deployment Architecture&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="20" width="370" height="60" as="geometry" />
        </mxCell>
        <mxCell id="XRrvoEsBIDFymCDPDrOS-2" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="dev" target="migrate">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="65" y="270" as="sourcePoint" />
            <mxPoint x="115" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
