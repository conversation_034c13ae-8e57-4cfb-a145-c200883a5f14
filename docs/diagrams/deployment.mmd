flowchart LR
  Dev[Developer / CI]
  Dockerfile[Dockerfile (pnpm build, codegen, prune)]
  BuildPush[build-and-push.sh]
  ECRStack[CloudFormation: ecr.yaml]
  AppStack[CloudFormation: be-product-master.yaml]
  ECR[(ECR Repository)]
  ECS[ECS Service / Task]
  Migrate[run-migrations.sh]
  RDS[(RDS PostgreSQL)]

  Dev --> Dockerfile --> BuildPush --> ECR
  Dev --> ECRStack
  Dev --> AppStack --> ECS
  Migrate --> RDS
  ECR -.image-> ECS

