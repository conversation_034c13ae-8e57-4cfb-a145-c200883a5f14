<mxfile host="Electron" modified="2025-08-27T13:47:31.803Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.7.5 Chrome/114.0.5735.289 Electron/25.8.1 Safari/537.36" version="21.7.5" etag="ZwPae2CRp3Df-4a_nY0R" type="device">
  <diagram id="arch-1" name="Architecture">
    <mxGraphModel dx="1114" dy="841" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="client" value="Client / Consumers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="40" y="90" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="apigw" value="API Gateway (HTTP API)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="200" y="90" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="alb" value="Application Load Balancer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="200" y="170" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ecs" value="ECS Fargate Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="400" y="170" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="app" value="NestJS App\nHTTP 3000, Svc 3003" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="400" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rds" value="PostgreSQL (RDS)" style="shape=cylinder;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="30" y="240" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="s3" value="S3: File Uploads" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="700" y="310" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="qFile" value="SQS: offline processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="700" y="390" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="qFileDlq" value="DLQ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="890" y="390" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cloudwatch" value="CloudWatch Logs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="700" y="450" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="secrets" value="Secrets Manager\n(DATABASE_URL, DB password)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="210" y="320" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="servdisc" value="Service Discovery\n(AWS Cloud Map)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="680" y="110" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="ecr" value="ECR Repository" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cdeb8b;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="420" y="100" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="shopify" value="Shopify Admin API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="690" y="240" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="othersvcs" value="Other Microservices" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="870" y="100" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="e1" style="endArrow=classic;" parent="1" source="client" target="apigw" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e2" style="endArrow=classic;" parent="1" source="apigw" target="alb" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e3" style="endArrow=classic;" parent="1" source="alb" target="ecs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e4" style="endArrow=classic;" parent="1" source="ecs" target="app" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e5" style="endArrow=classic;" parent="1" source="app" target="rds" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e6" style="endArrow=classic;" parent="1" source="app" target="s3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="630" y="340" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="e8" style="endArrow=classic;" parent="1" source="app" target="qFile" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="640" y="415" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="e10" style="dashed=1;endArrow=classic;" parent="1" source="qFile" target="qFileDlq" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e11" style="endArrow=classic;" parent="1" source="app" target="cloudwatch" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="640" y="480" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="e12" style="endArrow=classic;" parent="1" source="secrets" target="app" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e13" style="endArrow=classic;" parent="1" source="othersvcs" target="servdisc" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="e14" style="endArrow=classic;" parent="1" source="servdisc" target="app" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="760" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="e15" style="endArrow=classic;" parent="1" source="ecr" target="ecs" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e16" style="endArrow=classic;" parent="1" source="app" target="shopify" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yNBfTOv0fZyDbPfLSJRx-1" value="&lt;font style=&quot;font-size: 32px;&quot;&gt;Microservice Architecture&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="310" width="400" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
