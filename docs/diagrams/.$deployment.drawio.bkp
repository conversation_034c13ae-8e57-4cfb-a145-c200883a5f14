<mxfile host="app.diagrams.net" modified="2025-08-27T00:00:00.000Z" agent="augment" version="22.0.8">
  <diagram id="deploy-1" name="Deployment">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- Nodes -->
        <mxCell id="dev" value="Developer / CI" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="20" y="60" width="140" height="60" as="geometry" />
        </mxCell>

        <mxCell id="dockerfile" value="Dockerfile\n(pnpm build, codegen, prune)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="200" y="40" width="220" height="70" as="geometry" />
        </mxCell>

        <mxCell id="buildpush" value="build-and-push.sh" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="460" y="50" width="180" height="50" as="geometry" />
        </mxCell>

        <mxCell id="ecr" value="ECR Repository" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cdeb8b;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="660" y="40" width="160" height="70" as="geometry" />
        </mxCell>

        <mxCell id="ecrstack" value="CloudFormation\nECR stack (ecr.yaml)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="200" y="160" width="220" height="70" as="geometry" />
        </mxCell>

        <mxCell id="appstack" value="CloudFormation\nApp stack (be-product-master.yaml)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="460" y="160" width="280" height="70" as="geometry" />
        </mxCell>

        <mxCell id="ecs" value="ECS Service / Task Update" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="780" y="160" width="220" height="70" as="geometry" />
        </mxCell>

        <mxCell id="migrate" value="run-migrations.sh\n(prisma migrate deploy)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="200" y="280" width="260" height="70" as="geometry" />
        </mxCell>

        <mxCell id="rds" value="RDS PostgreSQL" style="shape=cylinder;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="500" y="280" width="200" height="80" as="geometry" />
        </mxCell>

        <!-- Edges -->
        <mxCell id="e1" edge="1" parent="1" source="dev" target="dockerfile" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e2" edge="1" parent="1" source="dockerfile" target="buildpush" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e3" edge="1" parent="1" source="buildpush" target="ecr" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e4" edge="1" parent="1" source="dev" target="ecrstack" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e5" edge="1" parent="1" source="dev" target="appstack" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e6" edge="1" parent="1" source="appstack" target="ecs" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e7" edge="1" parent="1" source="migrate" target="rds" style="endArrow=classic;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="e8" edge="1" parent="1" source="ecr" target="ecs" style="endArrow=classic;dashed=1;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

