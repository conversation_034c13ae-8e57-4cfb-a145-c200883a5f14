flowchart LR
  %% Simplified for diagrams.net Mermaid importer
  Client[Client / Consumers]
  APIGW[API Gateway]
  ALB[Application Load Balancer]
  ECSService[ECS Fargate Service]
  App[NestJS App (HTTP 3000, Svc 3003)]
  RDS[(PostgreSQL RDS)]
  S3[(S3 Bucket: Product File Uploads)]
  ProductsQueue[[SQS: products]]
  ProductsDLQ[[SQS DLQ: products-dlq]]
  FileUploadQueue[[SQS: product-file-upload]]
  FileUploadDLQ[[SQS DLQ: product-file-upload-dlq]]
  CloudWatch[CloudWatch Logs]
  Secrets[Secrets Manager: DATABASE_URL]
  ServiceDiscovery[AWS Cloud Map: Service Discovery]
  ECR[(ECR Repository)]
  Shopify[Shopify Admin API]
  OtherSvcs[Other Microservices]

  Client --> APIGW --> ALB --> ECSService --> App
  App --> RDS
  App --> S3
  App --> ProductsQueue
  App --> FileUploadQueue
  App --> Shopify
  App --> CloudWatch
  Secrets --> App
  OtherSvcs --> ServiceDiscovery
  ServiceDiscovery --- App
  ECR -.image-> ECSService
  ProductsQueue -.failed-> ProductsDLQ
  FileUploadQueue -.failed-> FileUploadDLQ

