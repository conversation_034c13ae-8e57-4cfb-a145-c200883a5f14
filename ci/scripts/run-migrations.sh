#!/bin/bash

# Run Prisma database migrations using ECS Fargate task
# This script runs migrations in the same private network as the application
# Usage: ./ci/scripts/run-migrations.sh [AWS_REGION] [ENVIRONMENT]

set -e

# Default values
AWS_REGION=${1:-eu-central-1}
ENVIRONMENT=${2:-production}
PROJECT_NAME="be-product-master"
PROJECT_PREFIX="ecom360"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️  Starting database migration via ECS for ${PROJECT_NAME}${NC}"
echo -e "${BLUE}📋 Configuration:${NC}"
echo -e "   Region: ${AWS_REGION}"
echo -e "   Environment: ${ENVIRONMENT}"
echo ""

# Check prerequisites
echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"

if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed${NC}"
    exit 1
fi

if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"
echo ""

# Get cluster and task definition info
CLUSTER_NAME="${PROJECT_NAME}-cluster"
TASK_DEFINITION_FAMILY="${PROJECT_NAME}-task"

echo -e "${YELLOW}🔍 Getting ECS cluster and task definition...${NC}"

# Get the latest task definition ARN
TASK_DEFINITION_ARN=$(aws ecs describe-task-definition \
    --region ${AWS_REGION} \
    --task-definition ${TASK_DEFINITION_FAMILY} \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

if [ -z "$TASK_DEFINITION_ARN" ] || [ "$TASK_DEFINITION_ARN" = "None" ]; then
    echo -e "${RED}❌ Could not find task definition '${TASK_DEFINITION_FAMILY}'${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found task definition${NC}"

# Get network configuration from the running service
SERVICE_NAME="${PROJECT_NAME}-service"
NETWORK_CONFIG=$(aws ecs describe-services \
    --region ${AWS_REGION} \
    --cluster ${CLUSTER_NAME} \
    --services ${SERVICE_NAME} \
    --query 'services[0].networkConfiguration.awsvpcConfiguration' \
    --output json)

if [ -z "$NETWORK_CONFIG" ] || [ "$NETWORK_CONFIG" = "null" ]; then
    echo -e "${RED}❌ Could not get network configuration from service '${SERVICE_NAME}'${NC}"
    exit 1
fi

SUBNETS=$(echo $NETWORK_CONFIG | jq -r '.subnets | join(",")')
SECURITY_GROUPS=$(echo $NETWORK_CONFIG | jq -r '.securityGroups | join(",")')

echo -e "${GREEN}✅ Network configuration retrieved${NC}"
echo ""

# Run the migration task
echo -e "${YELLOW}🚀 Starting migration task...${NC}"

TASK_ARN=$(aws ecs run-task \
    --region ${AWS_REGION} \
    --cluster ${CLUSTER_NAME} \
    --task-definition ${TASK_DEFINITION_ARN} \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[${SUBNETS}],securityGroups=[${SECURITY_GROUPS}],assignPublicIp=DISABLED}" \
    --overrides '{
        "containerOverrides": [
            {
                "name": "'${PROJECT_NAME}'",
                "command": ["sh", "-c", "pnpm prisma migrate deploy && echo \"Migration completed successfully\""]
            }
        ]
    }' \
    --query 'tasks[0].taskArn' \
    --output text)

if [ -z "$TASK_ARN" ] || [ "$TASK_ARN" = "None" ]; then
    echo -e "${RED}❌ Failed to start migration task${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 Migration task launched! Check AWS Console for progress.${NC}"


