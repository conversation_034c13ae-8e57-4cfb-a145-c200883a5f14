AWSTemplateFormatVersion: "2010-09-09"
Description: "Ecom360 Product Master Backend API infrastructure"

Parameters:
  ProjectName:
    Type: String
    Default: be-product-master
    Description: Name of this specific application

  ProjectPrefix:
    Type: String
    Default: ecom360
    Description: Prefix for shared resources (must match networking stack)

  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
    Description: Environment name (must match networking stack)

  ImageTag:
    Type: String
    Default: latest
    Description: Docker image tag to deploy

  DesiredCount:
    Type: Number
    Default: 1
    Description: Desired number of ECS tasks

  ContainerCpu:
    Type: Number
    Default: 256
    Description: CPU units for the container (256, 512, 1024, 2048, 4096)

  ContainerMemory:
    Type: Number
    Default: 512
    Description: Memory for the container in MB

  DBInstanceClass:
    Type: String
    Default: db.t3.micro
    Description: RDS instance class

  DBAllocatedStorage:
    Type: Number
    Default: 20
    Description: RDS allocated storage in GB

  DBName:
    Type: String
    Default: product_master
    Description: Database name

  DBUsername:
    Type: String
    Default: product_user
    Description: Database username

Resources:
  # Database Password Secret
  DBPasswordSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${ProjectPrefix}/${ProjectName}/db-password
      Description: Database password for Product Master
      GenerateSecretString:
        SecretStringTemplate: !Sub '{"username": "${DBUsername}"}'
        GenerateStringKey: 'password'
        PasswordLength: 32
        ExcludeCharacters: '"@/\:?#%&'
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-db-password
        - Key: Environment
          Value: !Ref Environment

  # Database URL Secret (constructed from RDS endpoint and credentials)
  DatabaseURLSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${ProjectPrefix}/${ProjectName}/database-url
      Description: Complete database URL for Product Master
      SecretString: !Sub |
        postgresql://${DBUsername}:{{resolve:secretsmanager:${DBPasswordSecret}:SecretString:password}}@${RDSInstance.Endpoint.Address}:5432/${DBName}?schema=public
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-database-url
        - Key: Environment
          Value: !Ref Environment

  # RDS Subnet Group (Private)
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupName: !Sub ${ProjectName}-db-subnet-group
      DBSubnetGroupDescription: Private Subnet group for Product Master RDS
      SubnetIds:
        - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-1-id
        - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-2-id
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-db-subnet-group
        - Key: Environment
          Value: !Ref Environment

  # RDS Security Group
  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub ${ProjectName}-rds-sg
      GroupDescription: Security group for Product Master RDS
      VpcId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-vpc-id
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
          SourceSecurityGroupId: !Ref ECSSecurityGroup
          Description: PostgreSQL access from ECS tasks
        - IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
          SourceSecurityGroupId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-jumpbox-sg-id
          Description: PostgreSQL access from jumpbox for database management
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-rds-sg
        - Key: Environment
          Value: !Ref Environment

  # Application-specific Security Group
  ECSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub ${ProjectName}-ecs-sg
      GroupDescription: Security group for Product Master ECS tasks
      VpcId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-vpc-id
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          SourceSecurityGroupId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-alb-sg-id
          Description: HTTP access from ALB
      SecurityGroupEgress:
        - IpProtocol: -1
          CidrIp: 0.0.0.0/0
          Description: All outbound traffic
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-sg
        - Key: Environment
          Value: !Ref Environment

  # Self-referencing ingress rule for microservice communication
  ECSSecurityGroupSelfIngress:
    Type: AWS::EC2::SecurityGroupIngress
    Properties:
      GroupId: !Ref ECSSecurityGroup
      IpProtocol: tcp
      FromPort: 3003
      ToPort: 3003
      SourceSecurityGroupId: !Ref ECSSecurityGroup
      Description: TCP microservice communication within ECS

  # RDS Instance
  RDSInstance:
    Type: AWS::RDS::DBInstance
    DeletionPolicy: Snapshot
    UpdateReplacePolicy: Snapshot
    Properties:
      DBInstanceIdentifier: !Sub ${ProjectName}-${Environment}-private
      DBInstanceClass: !Ref DBInstanceClass
      Engine: postgres
      EngineVersion: "15.8"
      AllocatedStorage: !Ref DBAllocatedStorage
      StorageType: gp2
      StorageEncrypted: true
      DBName: !Ref DBName
      MasterUsername: !Sub "{{resolve:secretsmanager:${DBPasswordSecret}:SecretString:username}}"
      MasterUserPassword: !Sub "{{resolve:secretsmanager:${DBPasswordSecret}:SecretString:password}}"
      VPCSecurityGroups:
        - !Ref RDSSecurityGroup
      DBSubnetGroupName: !Ref DBSubnetGroup
      BackupRetentionPeriod: 7
      PreferredBackupWindow: "03:00-04:00"
      PreferredMaintenanceWindow: "sun:04:00-sun:05:00"
      MultiAZ: false
      PubliclyAccessible: false
      DeletionProtection: false
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-rds
        - Key: Environment
          Value: !Ref Environment

  # CloudWatch Log Group
  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${ProjectName}
      RetentionInDays: 30
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-logs
        - Key: Environment
          Value: !Ref Environment

  # Dead Letter Queue for failed messages (must be created first)
  ProductMasterDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ProjectName}-${Environment}-products-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-dlq
        - Key: Environment
          Value: !Ref Environment

  # SQS Queue for Product Master (General)
  ProductMasterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ProjectName}-${Environment}-products-queue
      VisibilityTimeout: 300
      MessageRetentionPeriod: 1209600  # 14 days
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt ProductMasterDeadLetterQueue.Arn
        maxReceiveCount: 3
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-queue
        - Key: Environment
          Value: !Ref Environment

  # Dead Letter Queue for File Processing
  ProductFileUploadDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ProjectName}-${Environment}-product-file-upload-dlq
      MessageRetentionPeriod: 1209600  # 14 days
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-file-processing-dlq
        - Key: Environment
          Value: !Ref Environment

  # SQS Queue for File Processing
  ProductFileUploadQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub ${ProjectName}-${Environment}-product-file-upload-queue
      VisibilityTimeout: 900  # 15 minutes for file processing
      MessageRetentionPeriod: 1209600  # 14 days
      ReceiveMessageWaitTimeSeconds: 20  # Long polling
      RedrivePolicy:
        deadLetterTargetArn: !GetAtt ProductFileUploadDeadLetterQueue.Arn
        maxReceiveCount: 3
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-product-file-upload-queue
        - Key: Environment
          Value: !Ref Environment

  # S3 Bucket for File Storage
  FileStorageBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub ${ProjectName}-${Environment}-product-file-uploads-${AWS::AccountId}
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-files
        - Key: Environment
          Value: !Ref Environment

  # Service Discovery Service for Microservice (uses shared namespace)
  ServiceDiscoveryService:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: !Ref ProjectName
      NamespaceId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-service-discovery-namespace-id
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 60
          - Type: SRV
            TTL: 60
        RoutingPolicy: MULTIVALUE
      HealthCheckCustomConfig:
        FailureThreshold: 1
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-microservice-discovery
        - Key: Environment
          Value: !Ref Environment

  # IAM Roles
  ECSTaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ProjectName}-ecs-execution-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: ECRAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                Resource: "*"
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Ref DBPasswordSecret
                  - !Ref DatabaseURLSecret

      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-execution-role
        - Key: Environment
          Value: !Ref Environment

  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ProjectName}-ecs-task-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: !GetAtt LogGroup.Arn
        - PolicyName: DatabaseAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Ref DBPasswordSecret
                  - !Ref DatabaseURLSecret
        - PolicyName: SQSAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - sqs:SendMessage
                  - sqs:ReceiveMessage
                  - sqs:DeleteMessage
                  - sqs:GetQueueAttributes
                  - sqs:GetQueueUrl
                Resource:
                  - !GetAtt ProductMasterQueue.Arn
                  - !GetAtt ProductMasterDeadLetterQueue.Arn
                  - !GetAtt ProductFileUploadQueue.Arn
                  - !GetAtt ProductFileUploadDeadLetterQueue.Arn
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:GetObjectVersion
                Resource: !Sub "${FileStorageBucket.Arn}/*"
              - Effect: Allow
                Action:
                  - s3:ListBucket
                  - s3:GetBucketLocation
                Resource: !GetAtt FileStorageBucket.Arn
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-ecs-task-role
        - Key: Environment
          Value: !Ref Environment

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${ProjectName}-cluster
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      DefaultCapacityProviderStrategy:
        - CapacityProvider: FARGATE
          Weight: 1
        - CapacityProvider: FARGATE_SPOT
          Weight: 4
      ClusterSettings:
        - Name: containerInsights
          Value: enabled
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-cluster
        - Key: Environment
          Value: !Ref Environment

  # ALB Target Group (app-specific)
  ALBTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub ${ProjectName}-tg
      Port: 3000
      Protocol: HTTP
      VpcId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-vpc-id
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckPath: /api/productmaster/health
      HealthCheckProtocol: HTTP
      HealthCheckPort: 3000
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 5
      Matcher:
        HttpCode: "200"
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-tg
        - Key: Environment
          Value: !Ref Environment

  # ALB Listener Rule for /api/productmaster route
  # Manual Priority Assignment:
  ALBListenerRuleProductMaster:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-alb-listener-arn
      Priority: 130
      Conditions:
        - Field: path-pattern
          Values:
            - "/api/productmaster/*"
      Actions:
        - Type: forward
          TargetGroupArn: !Ref ALBTargetGroup

  # ECS Task Definition
  ECSTaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub ${ProjectName}-task
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: !Ref ContainerCpu
      Memory: !Ref ContainerMemory
      ExecutionRoleArn: !GetAtt ECSTaskExecutionRole.Arn
      TaskRoleArn: !GetAtt ECSTaskRole.Arn
      ContainerDefinitions:
        - Name: !Ref ProjectName
          Image: !Sub
            - "${ECRRepositoryURI}:${ImageTag}"
            - ECRRepositoryURI:
                Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-${ProjectName}-ecr-uri
          PortMappings:
            - ContainerPort: 3000
              Protocol: tcp
            - ContainerPort: 3003
              Protocol: tcp
          Essential: true
          Environment:
            - Name: NODE_ENV
              Value: production
            - Name: PORT
              Value: "3000"
            - Name: MICROSERVICE_PORT
              Value: "3003"
            - Name: SERVICE_DISCOVERY_NAMESPACE
              Value: !Sub ${ProjectPrefix}-${Environment}.local
            - Name: SERVICE_NAME
              Value: !Ref ProjectName
            - Name: TZ
              Value: UTC
            - Name: AWS_REGION
              Value: !Ref AWS::Region
            - Name: AWS_SQS_PRODUCTS_QUEUE_URL
              Value: !Ref ProductMasterQueue
            - Name: AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL
              Value: !Ref ProductFileUploadQueue
            - Name: AWS_S3_PRODUCT_FILE_UPLOAD_BUCKET_NAME
              Value: !Ref FileStorageBucket
          Secrets:
            - Name: DATABASE_URL
              ValueFrom: !Ref DatabaseURLSecret
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: ecs
          HealthCheck:
            Command:
              - CMD-SHELL
              - "curl -f http://localhost:3000/api/productmaster/health || exit 1"
            Interval: 30
            Timeout: 5
            Retries: 3
            StartPeriod: 120
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-task
        - Key: Environment
          Value: !Ref Environment

  # ECS Service
  ECSService:
    Type: AWS::ECS::Service
    CreationPolicy:
      ResourceSignal:
        Count: 0  # Don't wait for signals, just create the service
        Timeout: PT10M  # Maximum 10 minutes to create
    DependsOn:
      - ALBListenerRuleProductMaster
      - ECSSecurityGroupSelfIngress
    Properties:
      ServiceName: !Sub ${ProjectName}-service
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref ECSTaskDefinition
      DesiredCount: !Ref DesiredCount
      LaunchType: FARGATE
      PlatformVersion: LATEST
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets:
            - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-1-id
            - Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-private-subnet-2-id
          SecurityGroups:
            - !Ref ECSSecurityGroup
          AssignPublicIp: DISABLED
      LoadBalancers:
        - TargetGroupArn: !Ref ALBTargetGroup
          ContainerName: !Ref ProjectName
          ContainerPort: 3000
      ServiceRegistries:
        - RegistryArn: !GetAtt ServiceDiscoveryService.Arn
          Port: 3003
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
        DeploymentCircuitBreaker:
          Enable: true
          Rollback: true
      EnableExecuteCommand: true
      Tags:
        - Key: Name
          Value: !Sub ${ProjectName}-service
        - Key: Environment
          Value: !Ref Environment

  ApiGatewayRouteProductMaster:
    Type: AWS::ApiGatewayV2::Route
    Properties:
      ApiId:
        Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-id
      RouteKey: "ANY /api/productmaster/{proxy+}"
      Target: !Sub
        - "integrations/${IntegrationId}"
        - IntegrationId:
            Fn::ImportValue: !Sub ${ProjectPrefix}-${Environment}-api-integration-id
