AWSTemplateFormatVersion: "2010-09-09"
Description: "ECR repositories for Ecom360 applications"

Parameters:
  ProjectPrefix:
    Type: String
    Default: ecom360
    Description: Prefix for all resources

  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]
    Description: Environment name

  ProjectName:
    Type: String
    Default: be-product-master
    Description: Name of the ECR repository

Resources:
  # ECR Repository for Product Master
  ProductMasterECRRepository:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: !Ref ProjectName
      ImageScanningConfiguration:
        ScanOnPush: true
      EncryptionConfiguration:
        EncryptionType: AES256
      LifecyclePolicy:
        LifecyclePolicyText: |
          {
            "rules": [
              {
                "rulePriority": 1,
                "description": "Keep last 10 tagged images",
                "selection": {
                  "tagStatus": "tagged",
                  "tagPrefixList": ["v", "latest", "main", "develop"],
                  "countType": "imageCountMoreThan",
                  "countNumber": 10
                },
                "action": {
                  "type": "expire"
                }
              },
              {
                "rulePriority": 2,
                "description": "Delete untagged images older than 1 day",
                "selection": {
                  "tagStatus": "untagged",
                  "countType": "sinceImagePushed",
                  "countUnit": "days",
                  "countNumber": 1
                },
                "action": {
                  "type": "expire"
                }
              }
            ]
          }
      Tags:
        - Key: Name
          Value: !Sub ${ProjectPrefix}-${ProjectName}-ecr
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: !Ref ProjectName

Outputs:
  ProductMasterECRRepositoryURI:
    Description: ECR Repository URI for Product Master
    Value: !GetAtt ProductMasterECRRepository.RepositoryUri
    Export:
      Name: !Sub ${ProjectPrefix}-${Environment}-${ProjectName}-ecr-uri

  ProductMasterECRRepositoryName:
    Description: ECR Repository Name for Product Master
    Value: !Ref ProductMasterECRRepository
    Export:
      Name: !Sub ${ProjectPrefix}-${Environment}-${ProjectName}-ecr-name
