{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "assets": [{"include": "../config/*.yaml", "outDir": "./dist/config"}, {"include": "**/*.graphql", "exclude": "**/omitted.graphql", "watchAssets": true, "outDir": "dist/src"}], "tsConfigPath": "tsconfig.json"}, "monorepo": false, "root": ".", "graphql": {"typePaths": ["./**/*.graphql"], "path": "src/generated/graphql.ts", "watch": true}}