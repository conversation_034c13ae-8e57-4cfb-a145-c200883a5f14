COMPOSE_PROJECT_NAME=be-product-master
PORT=3000

MICROSERVICE_PORT=3003
SERVICE_NAME=be-product-master-microservice
SERVICE_DISCOVERY_NAMESPACE=

SHOP_MICROSERVICE_PORT=3005

DB_NAME=db-product-master
DB_USER=db-user
DB_PASSWORD=db-password
DB_PORT=5432
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@localhost:${DB_PORT}/${DB_NAME}?schema=public

AWS_SQS_PRODUCTS_QUEUE_URL=
AWS_SQS_PRODUCT_FILE_UPLOAD_QUEUE_URL=
AWS_S3_PRODUCT_FILE_UPLOAD_BUCKET_NAME=
# AWS credentials are only needed for local development
# In ECS, the application uses IAM roles automatically
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# Shopify Admin API Configuration
SHOPIFY_CONFIG_PATH=./config/shops.yaml
SHOPIFY_API_VERSION=2025-07
# Optional Configuration Overrides
# SHOPIFY_RETRIES=0
# SHOPIFY_USER_AGENT_PREFIX=SWECOM360/1.0
# Shopify Admin API Access Tokens (format: SHOPIFY_ACCESS_TOKEN_<SHOP_KEY_UPPERCASE>)
SHOPIFY_ACCESS_TOKEN_DEV1=
SHOPIFY_ACCESS_TOKEN_DEV2=
SHOPIFY_ACCESS_TOKEN_DEV3=

