###
# Creates a comment on a pull request that summarizes the changes in the PR.
#
###
name: AI Code Review

on:
  pull_request:

jobs:
  pr-review:
    name: 'Job: AI Review'
    runs-on: [ubuntu-24.04]
    permissions: write-all

    steps:
      - name: Set git to use LF
        run: |
          git config --global core.autocrlf false
          git config --global core.eol lf

      - name: Checkout git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Determine changes
        uses: GrantBirki/git-diff-action@v2
        id: git_diff_action
        with:
          raw_diff_file_output: diff.txt
          file_output_only: 'true'

      - name: Read changes from file
        id: read_diff_file
        run: |
          # Use awk to remove the entire diff block for: pnpm-lock.yaml, ai-code-review.yaml, examples and shopify codegen files
          awk '
          BEGIN { skip = 0 }
          /^diff --git a\/.*pnpm-lock\.yaml/ { skip = 1; next }
          /^diff --git a\/.*\.github\/workflows\/ai-code-review\.yaml/ { skip = 1; next }
          /^diff --git / { skip = 0 }
          skip == 0 { print }
          ' "${{ steps.git_diff_action.outputs.raw-diff-path }}" > filtered_diff.txt       
          delimiter="$(openssl rand -hex 8)"
          echo "file_content<<${delimiter}" >> "${GITHUB_OUTPUT}"
          cat filtered_diff.txt >> "${GITHUB_OUTPUT}"
          echo "${delimiter}" >> "${GITHUB_OUTPUT}"

      - name: Review changes with AI
        id: perform_ai_review
        uses: sw-ecom360/github-actions/ai-code-review@v1
        with:
          aws_access_key_id: ${{ secrets.AWS_KEY_CODE_REVIEW }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_CODE_REVIEW }}
          aws_region: eu-central-1
          git_diff_output: ${{ steps.read_diff_file.outputs.file_content }}
          pr_description: ${{ github.event.pull_request.body }}
          pr_title: ${{ github.event.pull_request.title }}

      - name: Add PR review comment thollander
        uses: thollander/actions-comment-pull-request@v3
        with:
          message: ${{ steps.perform_ai_review.outputs.message }}
          comment-tag: ai-code-review
          reactions: eyes