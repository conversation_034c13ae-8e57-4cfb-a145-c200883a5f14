name: PR Checks

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]

jobs:
  lint-and-build:
    name: Lint and Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 8

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install

      - name: Run linting
        run: pnpm run lint

      - name: Run build
        run: pnpm run build
