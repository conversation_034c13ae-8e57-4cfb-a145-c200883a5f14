import { join } from "path";
import {
  GenerateOptions,
  GraphQLFederationDefinitionsFactory,
} from "@nestjs/graphql";

async function generate() {
  const definitionsFactory = new GraphQLFederationDefinitionsFactory();

  const options: GenerateOptions = {
    typePaths: ["./src/**/*.graphql"],
    path: join(process.cwd(), "generated/api/graphql.ts"),
    outputAs: "class",
    watch: process.argv.includes("--watch"),
    debug: process.env.NODE_ENV === "development",
    defaultScalarType: "unknown",
    customScalarTypeMapping: {
      BigInt: "bigint",
      DateTime: "Date",
      JSON: "Record<string, unknown>",
      File: "globalThis.File",
    },
  };
  await definitionsFactory.generate(options);
}

void generate();
